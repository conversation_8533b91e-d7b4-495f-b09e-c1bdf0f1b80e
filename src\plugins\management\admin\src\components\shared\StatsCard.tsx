import React from 'react';
import {
  StatCard,
  StatContent,
  StatInfo,
  StatTitle,
  StatValue,
  StatIcon,
} from './StyledComponents';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color?: 'bg-blue' | 'bg-blue-dark' | 'bg-green' | 'bg-red' | string;
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, icon, color = '#3b82f6' }) => {
  return (
    <StatCard>
      <StatContent>
        <StatInfo>
          <StatTitle>{title}</StatTitle>
          <StatValue>{value}</StatValue>
        </StatInfo>
        <StatIcon $color={color}>{icon}</StatIcon>
      </StatContent>
    </StatCard>
  );
};

export default StatsCard;
